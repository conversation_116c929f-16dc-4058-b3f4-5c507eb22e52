// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class shipcheckai_Migrations : global::Aspire.Hosting.IProjectMetadata
{
    public string ProjectPath => """C:\Users\<USER>\Documents\Projects\NET\shipcheckai\shipcheckai.Migrations\shipcheckai.Migrations.csproj""";
}
