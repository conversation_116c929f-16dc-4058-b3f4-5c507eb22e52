<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsAspireSharedProject>true</IsAspireSharedProject>
    </PropertyGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App"/>
        <PackageReference Include="Aspire.Npgsql" Version="9.3.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.6" />

        <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.4.0"/>
        <PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="9.3.1"/>
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\shipcheckai.Entities\shipcheckai.Entities.csproj" />
    </ItemGroup>

</Project>
