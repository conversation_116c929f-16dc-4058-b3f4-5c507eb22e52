import {useState} from 'react';

function App() {
  const [count, setCount] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleYesClick = async () => {
    setLoading(true);
    try {
      const response = await fetch('api/bump', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCount(data.count);
      } else {
        console.error('Failed to fetch:', response.statusText);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="text-5xl">
      {count == null ?
        <div> sosal?
          <button onClick={handleYesClick} disabled={loading} className="mt-4 px-4 py-2 rounded">yes</button>
        </div>

        : <div>sosal {count} raz</div>}
    </div>
  );
}

export default App;